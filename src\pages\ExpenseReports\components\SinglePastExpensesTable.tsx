import React, { useState } from "react";
import Pagination from "../../../components/Pagination";

const ITEMS_PER_PAGE = 5;

const SinglePastExpensesTable: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  const pastExpenses = [
    {
      id: 1,
      expenseType: "Travel",
      submissionDate: "2025-06-01",
      amount: "₹12,000",
      status: "Completed",
      approvalStatus: "Approved",
    },
    {
      id: 2,
      expenseType: "Meals",
      submissionDate: "2025-06-10",
      amount: "₹1,500",
      status: "Completed",
      approvalStatus: "Rejected",
    },
    {
      id: 3,
      expenseType: "Lodging",
      submissionDate: "2025-06-15",
      amount: "₹8,750",
      status: "Completed",
      approvalStatus: "Approved",
    },
    {
      id: 4,
      expenseType: "Supplies",
      submissionDate: "2025-06-20",
      amount: "₹2,300",
      status: "Completed",
      approvalStatus: "Approved",
    },
    {
      id: 5,
      expenseType: "Conference",
      submissionDate: "2025-06-25",
      amount: "₹5,000",
      status: "Completed",
      approvalStatus: "Rejected",
    },
    {
      id: 6,
      expenseType: "Client Meeting",
      submissionDate: "2025-07-01",
      amount: "₹3,200",
      status: "Completed",
      approvalStatus: "Approved",
    },
  ];

  const totalPages = Math.ceil(pastExpenses.length / ITEMS_PER_PAGE);
  const paginatedExpenses = pastExpenses.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 gap-4">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">
            Past Expenses
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[720px] mt-4">
            <thead>
              <tr className="border-b border-gray-200 text-left text-sm text-gray-800">
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Expense Type</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Submission Date</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Total Amount</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Status</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Approval Status</th>
                <th className="py-3 px-2 sm:px-4 font-medium text-nowrap">Actions</th>
              </tr>
            </thead>
            <tbody>
              {paginatedExpenses.map((expense) => (
                <tr key={expense.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-2 sm:px-4">
                      <div className="flex items-start gap-2">
                      <img src="/categoryIcon.svg" alt="Category Icon" />
                        <div className="text-sm font-medium text-nowrap text-gray-500">
                          {expense.expenseType}
                        </div>                    
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-xs font-semibold text-gray-600 text-nowrap">
                    {expense.submissionDate}
                  </td>
                  <td className="py-4 px-2 sm:px-4 text-sm font-medium text-gray-600 text-nowrap">
                    {expense.amount}
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <span className="text-nowrap bg-gray-100 text-gray-700 px-2.5 py-1 text-xs rounded-full">
                      {expense.status}
                    </span>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <span
                      className={`px-2.5 py-1 text-xs rounded-full text-white text-nowrap ${
                        expense.approvalStatus === "Rejected"
                          ? "bg-red-500"
                          : "bg-green-500"
                      }`}
                    >
                      {expense.approvalStatus}
                    </span>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <button className="text-xs font-bold text-gray-500 hover:text-gray-800 text-nowrap">
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  );
};

export default SinglePastExpensesTable;
